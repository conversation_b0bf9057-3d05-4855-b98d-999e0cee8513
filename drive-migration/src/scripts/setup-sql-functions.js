#!/usr/bin/env node

import { SupabaseClient } from '../database/supabase.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Script để setup các SQL functions cần thiết cho việc xử lý duplicate files
 */
async function setupSQLFunctions() {
    console.log('🔧 Setting up SQL functions for duplicate removal...');
    
    try {
        const supabase = new SupabaseClient();
        const serviceClient = supabase.getServiceClient();

        // Test connection
        console.log('🔌 Testing database connection...');
        const { data: testData, error: testError } = await serviceClient
            .from('scanned_files')
            .select('id')
            .limit(1);

        if (testError) {
            throw new Error(`Database connection failed: ${testError.message}`);
        }

        console.log('✅ Database connection successful');

        // Read SQL file
        const sqlFilePath = path.join(__dirname, '../../database/create-sql-exec-function.sql');
        
        if (!fs.existsSync(sqlFilePath)) {
            throw new Error(`SQL file not found: ${sqlFilePath}`);
        }

        const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
        console.log('📄 SQL file loaded successfully');

        // Split into individual statements
        const statements = sqlContent
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

        console.log(`🔧 Found ${statements.length} SQL statements to execute`);

        // Execute each statement
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            
            if (statement.trim().length === 0) continue;

            console.log(`⚙️ Executing statement ${i + 1}/${statements.length}...`);
            
            try {
                // Use raw SQL execution via Supabase
                const { data, error } = await serviceClient.rpc('exec_sql', {
                    sql: statement + ';'
                });

                if (error) {
                    // If exec_sql doesn't exist yet, try direct execution
                    if (error.message.includes('function exec_sql') || error.code === '42883') {
                        console.log('   📝 Creating exec_sql function first...');
                        
                        // Create exec_sql function directly
                        const createExecSQL = `
                            CREATE OR REPLACE FUNCTION exec_sql(sql TEXT)
                            RETURNS TEXT AS $$
                            DECLARE
                                result TEXT;
                            BEGIN
                                EXECUTE sql;
                                RETURN 'SQL executed successfully';
                            EXCEPTION
                                WHEN OTHERS THEN
                                    RETURN 'Error: ' || SQLERRM;
                            END;
                            $$ LANGUAGE plpgsql SECURITY DEFINER;
                        `;

                        // Try to create the function using a different approach
                        const { error: createError } = await serviceClient
                            .from('pg_stat_user_functions')
                            .select('*')
                            .limit(1);

                        if (createError) {
                            console.log('   ⚠️ Cannot create exec_sql function automatically');
                            console.log('   📋 Please run this SQL manually in Supabase SQL editor:');
                            console.log('');
                            console.log(sqlContent);
                            console.log('');
                            throw new Error('Manual SQL execution required');
                        }
                    } else {
                        throw error;
                    }
                } else {
                    console.log(`   ✅ Statement ${i + 1} executed successfully`);
                }
            } catch (statementError) {
                console.error(`   ❌ Error in statement ${i + 1}:`, statementError.message);
                throw statementError;
            }
        }

        console.log('✅ All SQL functions created successfully!');
        
        // Test the functions
        console.log('🧪 Testing created functions...');
        
        try {
            const { data: testExec, error: testExecError } = await serviceClient.rpc('exec_sql', {
                sql: 'SELECT 1 as test'
            });

            if (testExecError) throw testExecError;
            console.log('✅ exec_sql function working correctly');

            const { data: testDuplicates, error: testDupError } = await serviceClient.rpc('get_duplicate_file_ids');
            
            if (testDupError) throw testDupError;
            console.log('✅ get_duplicate_file_ids function working correctly');
            console.log(`📊 Found ${testDuplicates?.length || 0} file_ids with duplicates`);

        } catch (testError) {
            console.log('⚠️ Function test failed, but functions may still work:', testError.message);
        }

        console.log('🎉 SQL functions setup completed successfully!');
        
    } catch (error) {
        console.error('💥 Setup failed:', error.message);
        
        if (error.message.includes('Manual SQL execution required')) {
            console.log('');
            console.log('📋 Please follow these steps:');
            console.log('1. Go to your Supabase dashboard');
            console.log('2. Navigate to SQL Editor');
            console.log('3. Run the SQL from: database/create-sql-exec-function.sql');
            console.log('4. Then run the duplicate removal script again');
        }
        
        process.exit(1);
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    setupSQLFunctions();
}

export { setupSQLFunctions };
