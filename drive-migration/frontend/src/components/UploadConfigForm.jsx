import React, { useState, useEffect } from 'react';
import './UploadConfigForm.css';

/**
 * Upload Config Form Component
 * Form cấu hình upload session với các tùy chọn:
 * - Tên session, users, thư mục gốc
 * - C<PERSON>u hình upload (concurrent, retry, file size limit)
 * - Filter mime types, duplicate handling
 * - Bandwidth throttling, validation
 */
const UploadConfigForm = ({
    users,
    sessions,
    onCreateSession,
    onStartSession,
    onViewSession,
    loading,
    currentSession
}) => {
    const [formData, setFormData] = useState({
        name: '',
        selectedUsers: [],
        rootFolderPath: 'E:\\', // Default path
        larkTargetFolder: '',
        concurrentUploads: 10,
        maxRetries: 1,
        skipMimeTypes: 'application/vnd.google-apps.folder, application/vnd.google-apps.shortcut',
        maxFileSize: 100, // MB
        duplicateHandling: 'skip',
        batchSize: 50,
        bandwidthLimit: '', // KB/s
        validateUpload: true
    });

    const [selectAll, setSelectAll] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [sortBy, setSortBy] = useState('file_count'); // file_count, total_size, user_email
    const [errors, setErrors] = useState({});
    const [showAdvanced, setShowAdvanced] = useState(false);

    // Auto-generate session name on component mount
    useEffect(() => {
        if (!formData.name && sessions && sessions.length >= 0) {
            const nextSessionNumber = sessions.length + 1;
            setFormData(prev => ({
                ...prev,
                name: `Lần chạy ${nextSessionNumber}`
            }));
        }
    }, [sessions]);

    // Handle form input changes
    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));

        // Clear error for this field
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: null
            }));
        }
    };

    // Handle user selection
    const handleUserSelect = (userEmail) => {
        setFormData(prev => {
            const isSelected = prev.selectedUsers.includes(userEmail);
            const newSelectedUsers = isSelected
                ? prev.selectedUsers.filter(email => email !== userEmail)
                : [...prev.selectedUsers, userEmail];

            return {
                ...prev,
                selectedUsers: newSelectedUsers
            };
        });
    };

    // Handle select all users
    const handleSelectAllUsers = () => {
        const newSelectAll = !selectAll;
        setSelectAll(newSelectAll);

        if (newSelectAll) {
            setFormData(prev => ({
                ...prev,
                selectedUsers: filteredUsers.map(user => user.user_email)
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                selectedUsers: []
            }));
        }
    };

    // Filter and sort users
    const filteredUsers = users
        .filter(user => {
            const searchLower = searchTerm.toLowerCase();
            return user.user_email.toLowerCase().includes(searchLower);
        })
        .sort((a, b) => {
            switch (sortBy) {
                case 'file_count':
                    return (b.file_count || 0) - (a.file_count || 0);
                case 'total_size':
                    return (b.total_size || 0) - (a.total_size || 0);
                case 'user_email':
                default:
                    return a.user_email.localeCompare(b.user_email);
            }
        });

    // Validate form
    const validateForm = () => {
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = 'Tên session là bắt buộc';
        }

        if (formData.selectedUsers.length === 0) {
            newErrors.selectedUsers = 'Phải chọn ít nhất một user';
        }

        if (!formData.rootFolderPath.trim()) {
            newErrors.rootFolderPath = 'Đường dẫn thư mục gốc là bắt buộc';
        }

        if (formData.concurrentUploads < 1 || formData.concurrentUploads > 50) {
            newErrors.concurrentUploads = 'Số upload đồng thời phải từ 1-50';
        }

        if (formData.maxRetries < 0 || formData.maxRetries > 10) {
            newErrors.maxRetries = 'Số lần retry phải từ 0-10';
        }

        if (formData.maxFileSize < 1 || formData.maxFileSize > 10000) {
            newErrors.maxFileSize = 'Giới hạn file size phải từ 1-10000 MB';
        }

        if (formData.batchSize < 1 || formData.batchSize > 1000) {
            newErrors.batchSize = 'Batch size phải từ 1-1000';
        }

        if (formData.bandwidthLimit && (isNaN(formData.bandwidthLimit) || formData.bandwidthLimit < 1)) {
            newErrors.bandwidthLimit = 'Bandwidth limit phải là số dương';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submit
    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        try {
            const sessionData = {
                ...formData,
                maxFileSize: formData.maxFileSize * 1024 * 1024, // Convert MB to bytes
                skipMimeTypes: formData.skipMimeTypes
                    ? formData.skipMimeTypes.split(',').map(type => type.trim()).filter(Boolean)
                    : [],
                bandwidthLimit: formData.bandwidthLimit ? parseInt(formData.bandwidthLimit) : null
            };

            const session = await onCreateSession(sessionData);

            // Ask if user wants to start immediately
            if (window.confirm('Tạo session thành công! Bạn có muốn bắt đầu upload ngay không?')) {
                await onStartSession(session.id);
            }
        } catch (error) {
            console.error('Error creating session:', error);
        }
    };

    // Format file size
    const formatFileSize = (bytes) => {
        if (!bytes) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <div className="upload-config-form">
            <div className="form-header">
                <h2>📋 Cấu hình Upload Session</h2>
                <p>Thiết lập các thông số để upload files lên Lark Drive</p>
            </div>

            <form onSubmit={handleSubmit} className="config-form">
                {/* Basic Configuration */}
                <div className="form-section">
                    <h3>🔧 Cấu hình cơ bản</h3>

                    <div className="form-row">
                        <div className="form-group">
                            <label htmlFor="name">Tên Session *</label>
                            <input
                                type="text"
                                id="name"
                                name="name"
                                value={formData.name}
                                onChange={handleChange}
                                placeholder="VD: Upload files 2025-01-16"
                                className={errors.name ? 'error' : ''}
                            />
                            {errors.name && <span className="error-message">{errors.name}</span>}
                        </div>

                        <div className="form-group">
                            <label htmlFor="rootFolderPath">Thư mục gốc *</label>
                            <input
                                type="text"
                                id="rootFolderPath"
                                name="rootFolderPath"
                                value={formData.rootFolderPath}
                                onChange={handleChange}
                                placeholder="VD: E:\\"
                                className={errors.rootFolderPath ? 'error' : ''}
                            />
                            {errors.rootFolderPath && <span className="error-message">{errors.rootFolderPath}</span>}
                        </div>
                    </div>

                    <div className="form-row">
                        <div className="form-group">
                            <label htmlFor="larkTargetFolder">Thư mục đích trong Lark (tùy chọn)</label>
                            <input
                                type="text"
                                id="larkTargetFolder"
                                name="larkTargetFolder"
                                value={formData.larkTargetFolder}
                                onChange={handleChange}
                                placeholder="VD: /Migration/2025"
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="duplicateHandling">Xử lý file trùng</label>
                            <select
                                id="duplicateHandling"
                                name="duplicateHandling"
                                value={formData.duplicateHandling}
                                onChange={handleChange}
                            >
                                <option value="skip">Bỏ qua</option>
                                <option value="overwrite">Ghi đè</option>
                                <option value="rename">Đổi tên</option>
                            </select>
                        </div>
                    </div>
                </div>

                {/* User Selection */}
                <div className="form-section">
                    <h3>👥 Chọn Users ({formData.selectedUsers.length} đã chọn)</h3>

                    <div className="user-controls">
                        <div className="search-sort-controls">
                            <input
                                type="text"
                                placeholder="Tìm kiếm user..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="search-input"
                            />

                            <select
                                value={sortBy}
                                onChange={(e) => setSortBy(e.target.value)}
                                className="sort-select"
                            >
                                <option value="file_count">Số file</option>
                                <option value="total_size">Dung lượng</option>
                                <option value="user_email">Email</option>
                            </select>
                        </div>

                        <button
                            type="button"
                            onClick={handleSelectAllUsers}
                            className="btn btn-secondary"
                        >
                            {selectAll ? 'Bỏ chọn tất cả' : 'Chọn tất cả'}
                        </button>
                    </div>

                    <div className="user-list">
                        {filteredUsers.map(user => (
                            <div
                                key={user.user_email}
                                className={`user-item ${formData.selectedUsers.includes(user.user_email) ? 'selected' : ''}`}
                                onClick={() => handleUserSelect(user.user_email)}
                            >
                                <div className="user-info">
                                    <div className="user-email">{user.user_email}</div>
                                    <div className="user-stats">
                                        {user.file_count || 0} files • {formatFileSize(user.total_size || 0)}
                                    </div>
                                </div>
                                <div className="user-checkbox">
                                    <input
                                        type="checkbox"
                                        checked={formData.selectedUsers.includes(user.user_email)}
                                        onChange={() => { }} // Handled by onClick
                                    />
                                </div>
                            </div>
                        ))}
                    </div>

                    {errors.selectedUsers && <span className="error-message">{errors.selectedUsers}</span>}
                </div>

                {/* Advanced Configuration */}
                <div className="form-section">
                    <div className="section-header" onClick={() => setShowAdvanced(!showAdvanced)}>
                        <h3>⚙️ Cấu hình nâng cao</h3>
                        <span className={`toggle-icon ${showAdvanced ? 'expanded' : ''}`}>▼</span>
                    </div>

                    {showAdvanced && (
                        <div className="advanced-config">
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="concurrentUploads">Upload đồng thời</label>
                                    <input
                                        type="number"
                                        id="concurrentUploads"
                                        name="concurrentUploads"
                                        value={formData.concurrentUploads}
                                        onChange={handleChange}
                                        min="1"
                                        max="50"
                                        className={errors.concurrentUploads ? 'error' : ''}
                                    />
                                    {errors.concurrentUploads && <span className="error-message">{errors.concurrentUploads}</span>}
                                </div>

                                <div className="form-group">
                                    <label htmlFor="maxRetries">Số lần retry</label>
                                    <input
                                        type="number"
                                        id="maxRetries"
                                        name="maxRetries"
                                        value={formData.maxRetries}
                                        onChange={handleChange}
                                        min="0"
                                        max="10"
                                        className={errors.maxRetries ? 'error' : ''}
                                    />
                                    {errors.maxRetries && <span className="error-message">{errors.maxRetries}</span>}
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="maxFileSize">Giới hạn file size (MB)</label>
                                    <input
                                        type="number"
                                        id="maxFileSize"
                                        name="maxFileSize"
                                        value={formData.maxFileSize}
                                        onChange={handleChange}
                                        min="1"
                                        max="1000"
                                        className={errors.maxFileSize ? 'error' : ''}
                                    />
                                    {errors.maxFileSize && <span className="error-message">{errors.maxFileSize}</span>}
                                </div>

                                <div className="form-group">
                                    <label htmlFor="batchSize">Batch size</label>
                                    <input
                                        type="number"
                                        id="batchSize"
                                        name="batchSize"
                                        value={formData.batchSize}
                                        onChange={handleChange}
                                        min="1"
                                        max="1000"
                                        className={errors.batchSize ? 'error' : ''}
                                    />
                                    {errors.batchSize && <span className="error-message">{errors.batchSize}</span>}
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="bandwidthLimit">Giới hạn băng thông (KB/s)</label>
                                    <input
                                        type="number"
                                        id="bandwidthLimit"
                                        name="bandwidthLimit"
                                        value={formData.bandwidthLimit}
                                        onChange={handleChange}
                                        placeholder="Để trống = không giới hạn"
                                        className={errors.bandwidthLimit ? 'error' : ''}
                                    />
                                    {errors.bandwidthLimit && <span className="error-message">{errors.bandwidthLimit}</span>}
                                </div>

                                <div className="form-group">
                                    <label htmlFor="skipMimeTypes">Bỏ qua MIME types</label>
                                    <input
                                        type="text"
                                        id="skipMimeTypes"
                                        name="skipMimeTypes"
                                        value={formData.skipMimeTypes}
                                        onChange={handleChange}
                                        placeholder="VD: application/vnd.google-apps.folder, text/plain"
                                    />
                                    <small>Phân cách bằng dấu phẩy</small>
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group checkbox-group">
                                    <label>
                                        <input
                                            type="checkbox"
                                            name="validateUpload"
                                            checked={formData.validateUpload}
                                            onChange={handleChange}
                                        />
                                        Kiểm tra tính toàn vẹn file sau upload
                                    </label>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Form Actions */}
                <div className="form-actions">
                    <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={loading}
                    >
                        {loading ? '⏳ Đang tạo...' : '🚀 Tạo Upload Session'}
                    </button>
                </div>
            </form>

            {/* Existing Sessions */}
            {sessions && sessions.length > 0 && (
                <div className="existing-sessions">
                    <h3>📋 Sessions gần đây</h3>
                    <div className="sessions-list">
                        {sessions.slice(0, 5).map(session => (
                            <div key={session.id} className="session-item">
                                <div className="session-info">
                                    <div className="session-name">{session.name}</div>
                                    <div className="session-stats">
                                        {session.status} • {session.uploaded_files || 0}/{session.total_files || 0} files
                                    </div>
                                    <div className="session-date">
                                        {new Date(session.created_at).toLocaleString()}
                                    </div>
                                </div>
                                <div className="session-actions">
                                    <button
                                        type="button"
                                        onClick={() => onViewSession(session.id)}
                                        className="btn btn-secondary btn-sm"
                                    >
                                        Xem
                                    </button>
                                    {session.status === 'pending' && (
                                        <button
                                            type="button"
                                            onClick={() => onStartSession(session.id)}
                                            className="btn btn-success btn-sm"
                                        >
                                            Bắt đầu
                                        </button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default UploadConfigForm;
