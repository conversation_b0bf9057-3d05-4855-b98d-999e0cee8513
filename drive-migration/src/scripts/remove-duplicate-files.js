#!/usr/bin/env node

import { SupabaseClient } from '../database/supabase.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Script để loại bỏ các bản ghi file_id trùng lặp trong bảng scanned_files
 * Ưu tiên giữ lại bản ghi cũ nhất (created_at sớm nhất)
 */
class DuplicateFileRemover {
    constructor() {
        this.supabase = new SupabaseClient();
        this.serviceClient = this.supabase.getServiceClient();
    }

    /**
     * Phân tích và đếm số lượng file_id trùng lặp
     */
    async analyzeDuplicates() {
        console.log('🔍 Analyzing duplicate file_id records...');

        try {
            // Đếm tổng số bản ghi
            const { count: totalCount, error: countError } = await this.serviceClient
                .from('scanned_files')
                .select('*', { count: 'exact', head: true });

            if (countError) throw countError;

            // Tìm các file_id bị trùng bằng cách query trực tiếp
            // Sử dụng raw SQL query thông qua Supabase
            const duplicateQuery = `
                SELECT file_id, COUNT(*) as duplicate_count
                FROM scanned_files
                GROUP BY file_id
                HAVING COUNT(*) > 1
                ORDER BY COUNT(*) DESC
            `;

            const { data: duplicatesData, error: queryError } = await this.serviceClient
                .rpc('exec_raw_sql', { query: duplicateQuery });

            if (queryError) {
                // Fallback: sử dụng JavaScript để tìm duplicates
                console.log('⚠️ Using fallback method to find duplicates...');
                console.log('📋 Loading all file_ids for analysis...');

                // Lấy tất cả file_id bằng cách phân trang
                let allFiles = [];
                let from = 0;
                const pageSize = 1000;

                while (true) {
                    const { data: pageData, error: pageError } = await this.serviceClient
                        .from('scanned_files')
                        .select('file_id')
                        .range(from, from + pageSize - 1);

                    if (pageError) throw pageError;

                    if (!pageData || pageData.length === 0) break;

                    allFiles = allFiles.concat(pageData);
                    console.log(`   📋 Loaded ${allFiles.length} records so far...`);

                    if (pageData.length < pageSize) break;
                    from += pageSize;
                }

                console.log(`📋 Retrieved ${allFiles.length} total records for analysis`);

                const fileIdCounts = {};
                allFiles.forEach(file => {
                    fileIdCounts[file.file_id] = (fileIdCounts[file.file_id] || 0) + 1;
                });

                const duplicates = Object.entries(fileIdCounts)
                    .filter(([fileId, count]) => count > 1)
                    .map(([file_id, duplicate_count]) => ({ file_id, duplicate_count }));

                console.log(`📊 Total records: ${totalCount}`);
                console.log(`🔄 Duplicate file_ids found: ${duplicates.length}`);

                return {
                    totalRecords: totalCount,
                    duplicateFileIds: duplicates.length,
                    duplicates: duplicates
                };
            }

            console.log(`📊 Total records: ${totalCount}`);
            console.log(`🔄 Duplicate file_ids found: ${duplicatesData?.length || 0}`);

            return {
                totalRecords: totalCount,
                duplicateFileIds: duplicatesData?.length || 0,
                duplicates: duplicatesData || []
            };

        } catch (error) {
            console.error('❌ Error analyzing duplicates:', error.message);
            throw error;
        }
    }

    /**
     * Tạo SQL function để tìm duplicates nếu chưa tồn tại
     */
    async createHelperFunctions() {
        console.log('🔧 Creating helper functions...');

        const createFunctionSQL = `
            CREATE OR REPLACE FUNCTION get_duplicate_file_ids()
            RETURNS TABLE(file_id TEXT, duplicate_count BIGINT) AS $$
            BEGIN
                RETURN QUERY
                SELECT sf.file_id, COUNT(*)::BIGINT as duplicate_count
                FROM scanned_files sf
                GROUP BY sf.file_id
                HAVING COUNT(*) > 1
                ORDER BY COUNT(*) DESC;
            END;
            $$ LANGUAGE plpgsql;
        `;

        try {
            const { error } = await this.serviceClient.rpc('exec_sql', {
                sql: createFunctionSQL
            });

            if (error) {
                console.log('⚠️ Could not create helper function, will use direct queries');
            } else {
                console.log('✅ Helper functions created successfully');
            }
        } catch (error) {
            console.log('⚠️ Could not create helper function, will use direct queries');
        }
    }

    /**
     * Xóa các bản ghi trùng lặp, giữ lại bản ghi cũ nhất
     */
    async removeDuplicates(batchSize = 500) {
        console.log('🗑️ Starting duplicate removal process...');

        try {
            // Tìm tất cả các file_id bị trùng
            const analysis = await this.analyzeDuplicates();

            if (analysis.duplicateFileIds === 0) {
                console.log('✅ No duplicates found, nothing to remove');
                return { success: true, deletedCount: 0 };
            }

            console.log(`🎯 Found ${analysis.duplicateFileIds} file_ids with duplicates`);
            console.log('🔄 Will keep the oldest record for each file_id');

            let totalDeleted = 0;
            let processedFileIds = 0;

            // Xử lý từng file_id một cách tuần tự
            for (const duplicate of analysis.duplicates) {
                processedFileIds++;
                const fileId = duplicate.file_id;
                const duplicateCount = duplicate.duplicate_count;

                console.log(`🔄 Processing file_id ${processedFileIds}/${analysis.duplicateFileIds}: ${fileId} (${duplicateCount} duplicates)`);

                // Lấy tất cả bản ghi của file_id này, sắp xếp theo created_at ASC
                const { data: records, error: recordsError } = await this.serviceClient
                    .from('scanned_files')
                    .select('id, created_at')
                    .eq('file_id', fileId)
                    .order('created_at', { ascending: true })
                    .order('id', { ascending: true });

                if (recordsError) throw recordsError;

                if (records.length <= 1) {
                    console.log(`   ⚠️ Skipping ${fileId} - only ${records.length} record found`);
                    continue;
                }

                // Giữ lại bản ghi đầu tiên (cũ nhất), xóa các bản ghi còn lại
                const recordsToDelete = records.slice(1);
                const idsToDelete = recordsToDelete.map(r => r.id);

                console.log(`   🗑️ Deleting ${idsToDelete.length} duplicate records for ${fileId}`);

                // Xóa theo batch nhỏ để tránh timeout
                for (let i = 0; i < idsToDelete.length; i += batchSize) {
                    const batchIds = idsToDelete.slice(i, i + batchSize);

                    const { error: deleteError } = await this.serviceClient
                        .from('scanned_files')
                        .delete()
                        .in('id', batchIds);

                    if (deleteError) throw deleteError;

                    totalDeleted += batchIds.length;
                    console.log(`   ✅ Deleted batch of ${batchIds.length} records (total: ${totalDeleted})`);

                    // Ngủ một chút để tránh overload
                    await new Promise(resolve => setTimeout(resolve, 50));
                }

                console.log(`   ✅ Completed ${fileId} - kept 1 record, deleted ${recordsToDelete.length}`);
            }

            console.log(`✅ Duplicate removal completed! Deleted ${totalDeleted} records`);

            return {
                success: true,
                deletedCount: totalDeleted
            };

        } catch (error) {
            console.error('❌ Error removing duplicates:', error.message);
            throw error;
        }
    }

    /**
     * Verify kết quả sau khi xóa
     */
    async verifyResults() {
        console.log('🔍 Verifying results...');

        try {
            const analysis = await this.analyzeDuplicates();

            if (analysis.duplicateFileIds === 0) {
                console.log('✅ SUCCESS: No duplicate file_ids found!');
                console.log(`📊 Total records remaining: ${analysis.totalRecords}`);
            } else {
                console.log(`⚠️ WARNING: Still found ${analysis.duplicateFileIds} duplicate file_ids`);
                console.log('🔄 You may need to run the script again');
            }

            return analysis;
        } catch (error) {
            console.error('❌ Error verifying results:', error.message);
            throw error;
        }
    }

    /**
     * Chạy toàn bộ process
     */
    async run() {
        try {
            console.log('🚀 Starting duplicate file removal process...');
            console.log('');

            // Phân tích duplicates
            const initialAnalysis = await this.analyzeDuplicates();
            console.log('');

            if (initialAnalysis.duplicateFileIds === 0) {
                console.log('✅ No duplicates found, script completed successfully!');
                return;
            }

            // Xóa duplicates
            const result = await this.removeDuplicates();
            console.log('');

            // Verify kết quả
            await this.verifyResults();
            console.log('');

            console.log('🎉 Duplicate removal process completed successfully!');

        } catch (error) {
            console.error('💥 Script failed:', error.message);
            process.exit(1);
        }
    }
}

// Chạy script nếu được gọi trực tiếp
if (import.meta.url === `file://${process.argv[1]}`) {
    const remover = new DuplicateFileRemover();
    remover.run();
}

export { DuplicateFileRemover };
