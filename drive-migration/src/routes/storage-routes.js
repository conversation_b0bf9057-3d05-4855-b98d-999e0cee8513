import express from "express";
import { storageScanner } from "../services/storage-scanner.js";
import { supabaseClient } from "../database/supabase.js";
import fs from "fs/promises";
import path from "path";

const router = express.Router();

/**
 * Start storage scan for users
 * POST /api/storage/scan
 */
router.post("/scan", async (req, res) => {
    try {
        const { userEmails, forceRefresh = false } = req.body;

        // Validate input
        if (!userEmails || !Array.isArray(userEmails) || userEmails.length === 0) {
            return res.status(400).json({
                error: "userEmails array is required",
            });
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const invalidEmails = userEmails.filter((email) => !emailRegex.test(email));
        if (invalidEmails.length > 0) {
            return res.status(400).json({
                error: "Invalid email format(s) found",
                invalidEmails,
            });
        }

        console.log(`📊 Starting storage scan for ${userEmails.length} users...`);

        // Start the storage scan
        const results = await storageScanner.scanAllUsersStorage(
            userEmails,
            forceRefresh
        );

        res.json({
            success: true,
            message: "Storage scan completed",
            results: {
                totalUsers: results.totalUsers,
                successful: results.success,
                failed: results.failed,
                errors: results.errors,
                duration: Date.now() - results.startTime,
            },
        });
    } catch (error) {
        console.error("❌ Error in storage scan:", error.message);
        res.status(500).json({
            error: "Failed to scan storage",
            details: error.message,
        });
    }
});

/**
 * Get storage statistics for all users
 * GET /api/storage/stats
 */
router.get("/stats", async (req, res) => {
    try {
        const {
            search = "",
            sortBy = "total_usage_bytes",
            sortOrder = "desc",
            page = 1,
            pageSize = 50,
        } = req.query;

        // Build query
        let query = supabaseClient
            .getServiceClient()
            .from("user_storage_stats")
            .select("*", { count: "exact" });

        // Apply search filter
        if (search) {
            query = query.ilike("user_email", `%${search}%`);
        }

        // Apply sorting
        const ascending = sortOrder === "asc";
        query = query.order(sortBy, { ascending });

        // Apply pagination
        const offset = (parseInt(page) - 1) * parseInt(pageSize);
        query = query.range(offset, offset + parseInt(pageSize) - 1);

        const { data: users, error, count } = await query;

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        // The scanned_storage_bytes is now stored in the user_storage_stats table
        // No need to calculate from scanned_files table each time

        // Get overall statistics
        const overallStats = await storageScanner.getStorageStatistics();

        res.json({
            users: users || [],
            pagination: {
                totalCount: count || 0,
                page: parseInt(page),
                pageSize: parseInt(pageSize),
                totalPages: Math.ceil((count || 0) / parseInt(pageSize)),
            },
            overallStats,
            success: true,
        });
    } catch (error) {
        console.error("❌ Error getting storage stats:", error.message);
        res.status(500).json({
            error: "Failed to get storage statistics",
            details: error.message,
        });
    }
});

/**
 * Get storage information for a specific user
 * GET /api/storage/user/:email
 */
router.get("/user/:email", async (req, res) => {
    try {
        const { email } = req.params;

        if (!email) {
            return res.status(400).json({
                error: "User email is required",
            });
        }

        const { data: user, error } = await supabaseClient
            .getServiceClient()
            .from("user_storage_stats")
            .select("*")
            .eq("user_email", email)
            .single();

        if (error && error.code !== "PGRST116") {
            throw new Error(`Database error: ${error.message}`);
        }

        if (!user) {
            return res.status(404).json({
                error: "User storage data not found",
                message: "Please run storage scan first",
            });
        }

        res.json({
            user,
            success: true,
        });
    } catch (error) {
        console.error("❌ Error getting user storage:", error.message);
        res.status(500).json({
            error: "Failed to get user storage information",
            details: error.message,
        });
    }
});

/**
 * Update local downloaded bytes for a user
 * PUT /api/storage/user/:email/downloaded
 */
router.put("/user/:email/downloaded", async (req, res) => {
    try {
        const { email } = req.params;
        const { localDownloadedBytes, localFolderPath } = req.body;

        if (!email) {
            return res.status(400).json({
                error: "User email is required",
            });
        }

        if (typeof localDownloadedBytes !== "number" || localDownloadedBytes < 0) {
            return res.status(400).json({
                error: "localDownloadedBytes must be a non-negative number",
            });
        }

        const updateData = {
            local_downloaded_bytes: localDownloadedBytes,
        };

        if (localFolderPath) {
            updateData.local_folder_path = localFolderPath;
        }

        const { error } = await supabaseClient
            .getServiceClient()
            .from("user_storage_stats")
            .update(updateData)
            .eq("user_email", email);

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        res.json({
            success: true,
            message: "Local downloaded bytes updated successfully",
        });
    } catch (error) {
        console.error("❌ Error updating downloaded bytes:", error.message);
        res.status(500).json({
            error: "Failed to update downloaded bytes",
            details: error.message,
        });
    }
});

/**
 * Calculate local folder sizes for all users
 * POST /api/storage/calculate-local-all
 */
router.post("/calculate-local-all", async (req, res) => {
    try {
        const { folderPath } = req.body;

        if (!folderPath) {
            return res.status(400).json({
                error: "folderPath is required",
            });
        }

        // Get all users from storage stats
        const { data: users, error: usersError } = await supabaseClient
            .getServiceClient()
            .from("user_storage_stats")
            .select("user_email");

        if (usersError) {
            throw new Error(`Database error: ${usersError.message}`);
        }

        if (!users || users.length === 0) {
            return res.status(404).json({
                error: "No users found in storage stats",
                message: "Please run storage scan first",
            });
        }

        const results = {
            successful: 0,
            failed: 0,
            errors: [],
            totalUsers: users.length,
            totalSize: 0,
        };

        // Calculate folder size for each user
        for (const user of users) {
            try {
                const userFolderPath = path.join(folderPath, user.user_email);
                const folderSize = await calculateFolderSize(userFolderPath);

                // Update database
                const { error } = await supabaseClient
                    .getServiceClient()
                    .from("user_storage_stats")
                    .update({
                        local_downloaded_bytes: folderSize,
                        local_folder_path: folderPath,
                    })
                    .eq("user_email", user.user_email);

                if (error) {
                    throw new Error(`Database error: ${error.message}`);
                }

                results.successful++;
                results.totalSize += folderSize;

                console.log(
                    `✅ Calculated local size for ${user.user_email}: ${formatBytes(
                        folderSize
                    )}`
                );
            } catch (error) {
                console.error(
                    `❌ Failed to calculate local size for ${user.user_email}:`,
                    error.message
                );
                results.failed++;
                results.errors.push({
                    email: user.user_email,
                    error: error.message,
                });
            }
        }

        res.json({
            success: true,
            message: "Batch local folder calculation completed",
            results: {
                ...results,
                totalSizeFormatted: formatBytes(results.totalSize),
            },
        });
    } catch (error) {
        console.error("❌ Error in batch local folder calculation:", error.message);
        res.status(500).json({
            error: "Failed to calculate local folder sizes",
            details: error.message,
        });
    }
});

/**
 * Recalculate scanned storage for all users or specific user
 * POST /api/storage/recalculate-scanned
 */
router.post("/recalculate-scanned", async (req, res) => {
    try {
        // Get all users
        let { data: allUsers, error: usersError } = await supabaseClient
            .getServiceClient()
            .from("scanned_users")
            .select("primary_email")
            .order("primary_email", { ascending: true });

        if (usersError) {
            throw new Error(`Database error: ${usersError.message}`);
        }

        // Get ALL files using pagination to handle large datasets
        let allFiles = [];
        let hasMore = true;
        let offset = 0;
        const batchSize = 1000;

        console.log(`📊 Fetching all scanned files with pagination...`);

        while (hasMore) {
            const { data: batch, error: batchError } = await supabaseClient
                .getServiceClient()
                .from("scanned_files_2")
                .select("file_id, user_email, size")
                .order("file_id")
                .range(offset, offset + batchSize - 1);

            if (batchError) {
                throw new Error(
                    `Database error while fetching batch: ${batchError.message}`
                );
            }

            if (batch && batch.length > 0) {
                allFiles = allFiles.concat(batch);
                offset += batchSize;
                hasMore = batch.length === batchSize;
                console.log(
                    `📄 Fetched ${batch.length} files (total so far: ${allFiles.length})`
                );
            } else {
                hasMore = false;
            }
        }

        console.log(`✅ Total files fetched: ${allFiles.length}`);

        // Group by user and calculate total size
        const scannedByUser = {};

        // Calculate total size for each user
        allUsers.forEach((user) => {
            const a = allFiles.filter(
                (file) => file.user_email === user.primary_email
            );

            scannedByUser[user.primary_email] = allFiles
                .filter((file) => file.user_email == user.primary_email)
                .reduce((result, file) => result + file.size, 0);
        });

        console.log(
            `📊 Calculated storage for ${Object.keys(scannedByUser).length} users`
        );

        // Update user_storage_stats with calculated scanned storage
        const updatePromises = Object.entries(scannedByUser).map(
            async ([email, totalSize]) => {
                const { error } = await supabaseClient
                    .getServiceClient()
                    .from("user_storage_stats")
                    .update({
                        scanned_storage_bytes: totalSize,
                        scanned_storage_updated_at: new Date().toISOString(),
                    })
                    .eq("user_email", email);

                if (error) {
                    console.error(
                        `Error updating scanned storage for ${email}:`,
                        error.message
                    );
                    return { email, error: error.message };
                }

                return { email, size: totalSize };
            }
        );

        const results = await Promise.all(updatePromises);
        const successful = results.filter((r) => !r.error);
        const failed = results.filter((r) => r.error);

        console.log(
            `✅ Recalculated scanned storage: ${successful.length} successful, ${failed.length} failed`
        );

        res.json({
            success: true,
            message: "Scanned storage recalculated successfully",
            results: {
                totalUsers: Object.keys(scannedByUser).length,
                successful: successful.length,
                failed: failed.length,
                errors: failed.map((f) => f.error),
                updatedUsers: successful,
            },
        });
    } catch (error) {
        console.error("❌ Error recalculating scanned storage:", error.message);
        res.status(500).json({
            error: "Failed to recalculate scanned storage",
            details: error.message,
        });
    }
});

/**
 * Calculate local folder size for a user
 * POST /api/storage/calculate-local/:email
 */
router.post("/calculate-local/:email", async (req, res) => {
    try {
        const { email } = req.params;
        const { folderPath } = req.body;

        if (!email) {
            return res.status(400).json({
                error: "User email is required",
            });
        }

        if (!folderPath) {
            return res.status(400).json({
                error: "folderPath is required",
            });
        }

        // Calculate folder size
        const userFolderPath = path.join(folderPath, email);
        const folderSize = await calculateFolderSize(userFolderPath);

        // Update database
        const { error } = await supabaseClient
            .getServiceClient()
            .from("user_storage_stats")
            .update({
                local_downloaded_bytes: folderSize,
                local_folder_path: folderPath,
            })
            .eq("user_email", email);

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        res.json({
            success: true,
            folderSize,
            folderSizeFormatted: formatBytes(folderSize),
            userFolderPath,
            message: "Local folder size calculated and updated",
        });
    } catch (error) {
        console.error("❌ Error calculating local folder size:", error.message);
        res.status(500).json({
            error: "Failed to calculate local folder size",
            details: error.message,
        });
    }
});

/**
 * Get detailed error information for users with scan errors
 * GET /api/storage/errors
 */
router.get("/errors", async (req, res) => {
    try {
        const { data: usersWithErrors, error } = await supabaseClient
            .getServiceClient()
            .from("user_storage_stats")
            .select("user_email, scan_error_message, last_scanned_at, drive_usage_bytes, scanned_storage_bytes, local_downloaded_bytes")
            .not("scan_error_message", "is", null)
            .order("last_scanned_at", { ascending: false });

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        // Enhance error data with additional analysis
        const enhancedErrors = usersWithErrors.map(user => {
            const scanPercentage = user.drive_usage_bytes > 0 ?
                ((user.scanned_storage_bytes || 0) / user.drive_usage_bytes * 100).toFixed(1) : 0;

            const downloadPercentage = user.scanned_storage_bytes > 0 ?
                ((user.local_downloaded_bytes || 0) / user.scanned_storage_bytes * 100).toFixed(1) : 0;

            return {
                ...user,
                scan_percentage: parseFloat(scanPercentage),
                download_percentage: parseFloat(downloadPercentage),
                missing_scan_bytes: Math.max(0, (user.drive_usage_bytes || 0) - (user.scanned_storage_bytes || 0)),
                missing_download_bytes: Math.max(0, (user.scanned_storage_bytes || 0) - (user.local_downloaded_bytes || 0))
            };
        });

        res.json({
            success: true,
            errors: enhancedErrors,
            totalErrorUsers: enhancedErrors.length,
            summary: {
                totalUsers: enhancedErrors.length,
                totalDriveUsage: enhancedErrors.reduce((sum, user) => sum + (user.drive_usage_bytes || 0), 0),
                totalScannedStorage: enhancedErrors.reduce((sum, user) => sum + (user.scanned_storage_bytes || 0), 0),
                totalDownloadedStorage: enhancedErrors.reduce((sum, user) => sum + (user.local_downloaded_bytes || 0), 0)
            }
        });
    } catch (error) {
        console.error("❌ Error getting storage errors:", error.message);
        res.status(500).json({
            error: "Failed to get storage errors",
            details: error.message,
        });
    }
});

/**
 * Analyze storage discrepancies between database and actual disk usage
 * GET /api/storage/analyze-discrepancies
 */
router.get("/analyze-discrepancies", async (req, res) => {
    try {
        const { folderPath = "E:\\" } = req.query;

        // Get all users with storage data
        const { data: users, error } = await supabaseClient
            .getServiceClient()
            .from("user_storage_stats")
            .select("*")
            .order("drive_usage_bytes", { ascending: false });

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        const analysis = {
            totalUsers: users.length,
            totalDbDownloaded: 0,
            totalActualDownloaded: 0,
            totalDiscrepancy: 0,
            userDiscrepancies: [],
            summary: {
                usersWithDiscrepancies: 0,
                largestDiscrepancy: 0,
                largestDiscrepancyUser: null,
                averageDiscrepancy: 0,
                totalMissingBytes: 0,
                totalExtraBytes: 0
            }
        };

        // Analyze each user
        for (const user of users) {
            try {
                const userFolderPath = path.join(folderPath, user.user_email);
                const actualSize = await calculateFolderSize(userFolderPath);
                const dbSize = user.local_downloaded_bytes || 0;
                const discrepancy = actualSize - dbSize;
                const discrepancyPercentage = dbSize > 0 ? (discrepancy / dbSize) * 100 : 0;

                analysis.totalDbDownloaded += dbSize;
                analysis.totalActualDownloaded += actualSize;

                const userAnalysis = {
                    user_email: user.user_email,
                    db_downloaded_bytes: dbSize,
                    actual_downloaded_bytes: actualSize,
                    discrepancy_bytes: discrepancy,
                    discrepancy_percentage: discrepancyPercentage,
                    drive_usage_bytes: user.drive_usage_bytes || 0,
                    scanned_storage_bytes: user.scanned_storage_bytes || 0,
                    has_significant_discrepancy: Math.abs(discrepancyPercentage) > 5, // More than 5% difference
                    folder_exists: actualSize > 0,
                    last_scanned_at: user.last_scanned_at,
                    scan_error_message: user.scan_error_message
                };

                analysis.userDiscrepancies.push(userAnalysis);

                // Update summary
                if (Math.abs(discrepancy) > Math.abs(analysis.summary.largestDiscrepancy)) {
                    analysis.summary.largestDiscrepancy = discrepancy;
                    analysis.summary.largestDiscrepancyUser = user.user_email;
                }

                if (userAnalysis.has_significant_discrepancy) {
                    analysis.summary.usersWithDiscrepancies++;
                }

                if (discrepancy < 0) {
                    analysis.summary.totalMissingBytes += Math.abs(discrepancy);
                } else if (discrepancy > 0) {
                    analysis.summary.totalExtraBytes += discrepancy;
                }

            } catch (error) {
                console.error(`Error analyzing user ${user.user_email}:`, error.message);
                analysis.userDiscrepancies.push({
                    user_email: user.user_email,
                    db_downloaded_bytes: user.local_downloaded_bytes || 0,
                    actual_downloaded_bytes: 0,
                    discrepancy_bytes: -(user.local_downloaded_bytes || 0),
                    discrepancy_percentage: -100,
                    drive_usage_bytes: user.drive_usage_bytes || 0,
                    scanned_storage_bytes: user.scanned_storage_bytes || 0,
                    has_significant_discrepancy: true,
                    folder_exists: false,
                    error: error.message,
                    last_scanned_at: user.last_scanned_at,
                    scan_error_message: user.scan_error_message
                });
            }
        }

        analysis.totalDiscrepancy = analysis.totalActualDownloaded - analysis.totalDbDownloaded;
        analysis.summary.averageDiscrepancy = analysis.userDiscrepancies.length > 0 ?
            analysis.userDiscrepancies.reduce((sum, user) => sum + user.discrepancy_bytes, 0) / analysis.userDiscrepancies.length : 0;

        // Sort by largest discrepancy
        analysis.userDiscrepancies.sort((a, b) => Math.abs(b.discrepancy_bytes) - Math.abs(a.discrepancy_bytes));

        res.json({
            success: true,
            analysis,
            folderPath,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error("❌ Error analyzing storage discrepancies:", error.message);
        res.status(500).json({
            error: "Failed to analyze storage discrepancies",
            details: error.message,
        });
    }
});

/**
 * Analyze file size discrepancies between Google Drive and downloaded files
 * GET /api/storage/analyze-file-sizes
 */
router.get("/analyze-file-sizes", async (req, res) => {
    try {
        const { userEmail, limit = 100 } = req.query;

        // Build query to get scanned files with their download information
        let query = supabaseClient
            .getServiceClient()
            .from("scanned_files")
            .select(`
        id, file_id, name, file_type, google_file_size,
        user_email, full_path, download_status, local_path,
        created_at, downloaded_at
      `)
            .not("google_file_size", "is", null)
            .order("google_file_size", { ascending: false })
            .limit(parseInt(limit));

        if (userEmail) {
            query = query.eq("user_email", userEmail);
        }

        const { data: files, error } = await query;

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        const analysis = {
            totalFiles: files.length,
            googleDocsFiles: [],
            binaryFiles: [],
            sizeDiscrepancies: [],
            summary: {
                totalGoogleDocs: 0,
                totalBinaryFiles: 0,
                filesWithSizeIncrease: 0,
                filesWithSizeDecrease: 0,
                averageSizeIncrease: 0,
                largestSizeIncrease: 0,
                largestSizeIncreaseFile: null,
                conversionStats: {
                    'application/vnd.google-apps.document': { count: 0, avgSizeIncrease: 0 },
                    'application/vnd.google-apps.spreadsheet': { count: 0, avgSizeIncrease: 0 },
                    'application/vnd.google-apps.presentation': { count: 0, avgSizeIncrease: 0 },
                    'application/vnd.google-apps.drawing': { count: 0, avgSizeIncrease: 0 }
                }
            }
        };

        // Google Docs MIME types and their export formats
        const googleDocsMimeTypes = {
            'application/vnd.google-apps.document': {
                exportFormat: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                extension: '.docx',
                name: 'Google Docs'
            },
            'application/vnd.google-apps.spreadsheet': {
                exportFormat: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                extension: '.xlsx',
                name: 'Google Sheets'
            },
            'application/vnd.google-apps.presentation': {
                exportFormat: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                extension: '.pptx',
                name: 'Google Slides'
            },
            'application/vnd.google-apps.drawing': {
                exportFormat: 'image/png',
                extension: '.png',
                name: 'Google Drawings'
            }
        };

        // Analyze each file
        for (const file of files) {
            const isGoogleDoc = googleDocsMimeTypes[file.file_type];
            const googleSize = file.google_file_size || 0;
            let actualSize = 0;
            let sizeDiscrepancy = 0;
            let hasLocalFile = false;

            // Try to get actual file size if downloaded
            if (file.local_path && file.download_status === 'downloaded') {
                try {
                    const stats = await fs.stat(file.local_path);
                    actualSize = stats.size;
                    hasLocalFile = true;
                    sizeDiscrepancy = actualSize - googleSize;
                } catch (error) {
                    // File might not exist or path is incorrect
                    console.warn(`Cannot access file: ${file.local_path}`);
                }
            }

            const fileAnalysis = {
                file_id: file.file_id,
                name: file.name,
                file_type: file.file_type,
                user_email: file.user_email,
                google_size: googleSize,
                actual_size: actualSize,
                size_discrepancy: sizeDiscrepancy,
                size_discrepancy_percentage: googleSize > 0 ? (sizeDiscrepancy / googleSize) * 100 : 0,
                has_local_file: hasLocalFile,
                download_status: file.download_status,
                is_google_doc: !!isGoogleDoc,
                export_info: isGoogleDoc || null,
                created_at: file.created_at,
                downloaded_at: file.downloaded_at
            };

            if (isGoogleDoc) {
                analysis.googleDocsFiles.push(fileAnalysis);
                analysis.summary.totalGoogleDocs++;

                // Update conversion stats
                if (analysis.summary.conversionStats[file.file_type]) {
                    analysis.summary.conversionStats[file.file_type].count++;
                    if (hasLocalFile) {
                        analysis.summary.conversionStats[file.file_type].avgSizeIncrease += sizeDiscrepancy;
                    }
                }
            } else {
                analysis.binaryFiles.push(fileAnalysis);
                analysis.summary.totalBinaryFiles++;
            }

            // Track size discrepancies
            if (hasLocalFile) {
                if (sizeDiscrepancy > 0) {
                    analysis.summary.filesWithSizeIncrease++;
                    if (sizeDiscrepancy > analysis.summary.largestSizeIncrease) {
                        analysis.summary.largestSizeIncrease = sizeDiscrepancy;
                        analysis.summary.largestSizeIncreaseFile = file.name;
                    }
                } else if (sizeDiscrepancy < 0) {
                    analysis.summary.filesWithSizeDecrease++;
                }

                analysis.sizeDiscrepancies.push(fileAnalysis);
            }
        }

        // Calculate averages for conversion stats
        Object.keys(analysis.summary.conversionStats).forEach(mimeType => {
            const stats = analysis.summary.conversionStats[mimeType];
            if (stats.count > 0) {
                stats.avgSizeIncrease = stats.avgSizeIncrease / stats.count;
            }
        });

        // Calculate overall average size increase
        const filesWithDiscrepancy = analysis.sizeDiscrepancies.filter(f => f.has_local_file);
        if (filesWithDiscrepancy.length > 0) {
            analysis.summary.averageSizeIncrease =
                filesWithDiscrepancy.reduce((sum, f) => sum + f.size_discrepancy, 0) / filesWithDiscrepancy.length;
        }

        // Sort by size discrepancy
        analysis.sizeDiscrepancies.sort((a, b) => b.size_discrepancy - a.size_discrepancy);

        res.json({
            success: true,
            analysis,
            googleDocsMimeTypes,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error("❌ Error analyzing file sizes:", error.message);
        res.status(500).json({
            error: "Failed to analyze file sizes",
            details: error.message,
        });
    }
});

/**
 * Calculate folder size recursively
 * @param {string} folderPath - Path to folder
 * @returns {Promise<number>} Total size in bytes
 */
async function calculateFolderSize(folderPath) {
    try {
        const stats = await fs.stat(folderPath);

        if (!stats.isDirectory()) {
            return stats.size;
        }

        const files = await fs.readdir(folderPath);
        let totalSize = 0;

        for (const file of files) {
            const filePath = path.join(folderPath, file);
            const fileStats = await fs.stat(filePath);

            if (fileStats.isDirectory()) {
                totalSize += await calculateFolderSize(filePath);
            } else {
                totalSize += fileStats.size;
            }
        }

        return totalSize;
    } catch (error) {
        if (error.code === "ENOENT") {
            console.log(`Folder not found: ${folderPath}`);
            return 0;
        }
        throw error;
    }
}

/**
 * Format bytes to human readable format
 * @param {number} bytes - Bytes
 * @returns {string} Formatted string
 */
function formatBytes(bytes) {
    if (!bytes || bytes === 0) return "0 B";

    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

export default router;
