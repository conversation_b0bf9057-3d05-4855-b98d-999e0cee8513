import React, { useState, useEffect } from 'react';
import { apiGet } from '../utils/apiUtils';
import { formatBytes } from '../utils/formatUtils';
import { useToast } from '../contexts/ToastContext';
import './StorageErrorsModal.css';

const StorageErrorsModal = ({ isOpen, onClose }) => {
    const [errors, setErrors] = useState([]);
    const [summary, setSummary] = useState(null);
    const [loading, setLoading] = useState(false);
    const [expandedError, setExpandedError] = useState(null);
    const { showError } = useToast();

    useEffect(() => {
        if (isOpen) {
            loadErrorData();
        }
    }, [isOpen]);

    const loadErrorData = async () => {
        setLoading(true);
        try {
            const response = await apiGet('/api/storage/errors');
            setErrors(response.errors);
            setSummary(response.summary);
        } catch (error) {
            console.error('Error loading storage errors:', error);
            showError('Không thể tải dữ liệu lỗi storage');
        } finally {
            setLoading(false);
        }
    };

    const toggleErrorDetails = (userEmail) => {
        setExpandedError(expandedError === userEmail ? null : userEmail);
    };

    const getErrorSeverity = (error) => {
        // Classify error severity based on message content
        const message = error.scan_error_message.toLowerCase();
        if (message.includes('permission') || message.includes('access')) {
            return 'permission';
        }
        if (message.includes('quota') || message.includes('limit')) {
            return 'quota';
        }
        if (message.includes('network') || message.includes('timeout')) {
            return 'network';
        }
        return 'other';
    };

    const getSeverityColor = (severity) => {
        switch (severity) {
            case 'permission': return '#ff6b6b';
            case 'quota': return '#ffa726';
            case 'network': return '#42a5f5';
            default: return '#9e9e9e';
        }
    };

    const getSeverityLabel = (severity) => {
        switch (severity) {
            case 'permission': return 'Lỗi quyền truy cập';
            case 'quota': return 'Lỗi quota/giới hạn';
            case 'network': return 'Lỗi mạng/timeout';
            default: return 'Lỗi khác';
        }
    };

    if (!isOpen) return null;

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content storage-errors-modal" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>Chi tiết lỗi Storage Scan</h2>
                    <button className="modal-close" onClick={onClose}>×</button>
                </div>

                <div className="modal-body">
                    {loading ? (
                        <div className="loading-state">
                            <div className="spinner"></div>
                            <p>Đang tải dữ liệu lỗi...</p>
                        </div>
                    ) : (
                        <>
                            {summary && (
                                <div className="error-summary">
                                    <h3>Tổng quan lỗi</h3>
                                    <div className="summary-stats">
                                        <div className="stat-item">
                                            <span className="stat-label">Tổng users có lỗi:</span>
                                            <span className="stat-value">{summary.totalUsers}</span>
                                        </div>
                                        <div className="stat-item">
                                            <span className="stat-label">Tổng Drive Usage:</span>
                                            <span className="stat-value">{formatBytes(summary.totalDriveUsage)}</span>
                                        </div>
                                        <div className="stat-item">
                                            <span className="stat-label">Đã scan được:</span>
                                            <span className="stat-value">{formatBytes(summary.totalScannedStorage)}</span>
                                        </div>
                                        <div className="stat-item">
                                            <span className="stat-label">Đã download:</span>
                                            <span className="stat-value">{formatBytes(summary.totalDownloadedStorage)}</span>
                                        </div>
                                    </div>
                                </div>
                            )}

                            <div className="errors-list">
                                <h3>Chi tiết lỗi từng user</h3>
                                {errors.length === 0 ? (
                                    <p className="no-errors">Không có lỗi nào được tìm thấy</p>
                                ) : (
                                    errors.map((error) => {
                                        const severity = getErrorSeverity(error);
                                        const isExpanded = expandedError === error.user_email;

                                        return (
                                            <div key={error.user_email} className="error-item">
                                                <div
                                                    className="error-header"
                                                    onClick={() => toggleErrorDetails(error.user_email)}
                                                >
                                                    <div className="error-user-info">
                                                        <span
                                                            className="severity-indicator"
                                                            style={{ backgroundColor: getSeverityColor(severity) }}
                                                            title={getSeverityLabel(severity)}
                                                        ></span>
                                                        <span className="user-email">{error.user_email}</span>
                                                        <span className="error-time">
                                                            {new Date(error.last_scanned_at).toLocaleString()}
                                                        </span>
                                                    </div>
                                                    <div className="error-stats">
                                                        <span className="scan-percentage">
                                                            Scan: {error.scan_percentage}%
                                                        </span>
                                                        <span className="download-percentage">
                                                            Download: {error.download_percentage}%
                                                        </span>
                                                        <span className="expand-icon">
                                                            {isExpanded ? '▼' : '▶'}
                                                        </span>
                                                    </div>
                                                </div>

                                                {isExpanded && (
                                                    <div className="error-details">
                                                        <div className="error-message">
                                                            <h4>Thông báo lỗi:</h4>
                                                            <pre className="error-text">{error.scan_error_message}</pre>
                                                        </div>

                                                        <div className="storage-breakdown">
                                                            <h4>Phân tích dung lượng:</h4>
                                                            <div className="storage-stats">
                                                                <div className="storage-stat">
                                                                    <span>Drive Usage:</span>
                                                                    <span>{formatBytes(error.drive_usage_bytes || 0)}</span>
                                                                </div>
                                                                <div className="storage-stat">
                                                                    <span>Đã scan:</span>
                                                                    <span>{formatBytes(error.scanned_storage_bytes || 0)}</span>
                                                                </div>
                                                                <div className="storage-stat">
                                                                    <span>Thiếu scan:</span>
                                                                    <span className="missing">{formatBytes(error.missing_scan_bytes)}</span>
                                                                </div>
                                                                <div className="storage-stat">
                                                                    <span>Đã download:</span>
                                                                    <span>{formatBytes(error.local_downloaded_bytes || 0)}</span>
                                                                </div>
                                                                <div className="storage-stat">
                                                                    <span>Thiếu download:</span>
                                                                    <span className="missing">{formatBytes(error.missing_download_bytes)}</span>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div className="error-classification">
                                                            <h4>Phân loại lỗi:</h4>
                                                            <span
                                                                className="severity-badge"
                                                                style={{ backgroundColor: getSeverityColor(severity) }}
                                                            >
                                                                {getSeverityLabel(severity)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })
                                )}
                            </div>
                        </>
                    )}
                </div>

                <div className="modal-footer">
                    <button className="btn btn-secondary" onClick={onClose}>
                        Đóng
                    </button>
                    <button className="btn btn-primary" onClick={loadErrorData} disabled={loading}>
                        Làm mới
                    </button>
                </div>
            </div>
        </div>
    );
};

export default StorageErrorsModal;
