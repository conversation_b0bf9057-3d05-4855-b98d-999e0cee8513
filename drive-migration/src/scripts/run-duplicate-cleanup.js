#!/usr/bin/env node

import { setupSQLFunctions } from './setup-sql-functions.js';
import { DuplicateFileRemover } from './remove-duplicate-files.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Main script để chạy toàn bộ quá trình cleanup duplicate files
 */
async function runDuplicateCleanup() {
    console.log('🚀 Starting complete duplicate file cleanup process...');
    console.log('='.repeat(60));
    console.log('');

    try {
        // Step 1: Setup SQL functions
        console.log('📋 STEP 1: Setting up required SQL functions');
        console.log('-'.repeat(40));
        await setupSQLFunctions();
        console.log('');

        // Step 2: Run duplicate removal
        console.log('📋 STEP 2: Running duplicate file removal');
        console.log('-'.repeat(40));
        const remover = new DuplicateFileRemover();
        await remover.run();
        console.log('');

        console.log('='.repeat(60));
        console.log('🎉 COMPLETE: Duplicate cleanup process finished successfully!');
        console.log('✅ Your scanned_files table should now have no duplicate file_ids');
        
    } catch (error) {
        console.error('💥 FAILED: Duplicate cleanup process failed');
        console.error('❌ Error:', error.message);
        console.log('');
        console.log('🔧 Troubleshooting tips:');
        console.log('1. Check your .env file has correct Supabase credentials');
        console.log('2. Ensure your Supabase service role key has admin permissions');
        console.log('3. Check database connectivity');
        console.log('4. If SQL functions fail, run them manually in Supabase SQL editor');
        
        process.exit(1);
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runDuplicateCleanup();
}

export { runDuplicateCleanup };
